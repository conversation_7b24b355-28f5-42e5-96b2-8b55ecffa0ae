{"name": "workly", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "start:expo-go": "node scripts/switch-expo-go.js expo-go && expo start --clear", "start:normal": "node scripts/switch-expo-go.js normal && expo start --clear", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "generate-icons": "node scripts/generate-icons.js", "icons": "npm run generate-icons", "switch:expo-go": "node scripts/switch-expo-go.js expo-go", "switch:normal": "node scripts/switch-expo-go.js normal", "bundle:optimize": "node scripts/optimize-bundle.js", "bundle:analyze": "npx expo export --dump-assetmap", "start:optimized": "npx expo start --clear --minify", "build:guide": "node scripts/build-android.js", "build:simple": "node scripts/simple-build.js", "build:dev-android": "eas build --platform android --profile development", "build:local-android": "eas build --platform android --profile development --local", "prebuild:android": "npx expo prebuild --platform android", "start:dev-client": "npx expo start --dev-client"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/native": "^7.1.9", "@react-navigation/stack": "^7.3.2", "date-fns": "^4.1.0", "expo": "53.0.13", "expo-constants": "^17.1.6", "expo-dev-client": "~5.2.2", "expo-font": "~13.3.1", "expo-linear-gradient": "~14.1.5", "expo-location": "^18.1.5", "expo-notifications": "~0.31.3", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-system-ui": "^5.0.9", "react": "19.0.0", "react-native": "0.79.4", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "jest-environment-jsdom": "^30.0.2", "typescript": "~5.8.3"}, "private": true, "keywords": ["workly", "shift-management", "attendance", "work-schedule"], "author": "Workly Team", "license": "MIT", "description": "Ứng dụng quản lý ca làm việc cá nhân với tính năng chấm công, nhắc nhở và thống kê"}